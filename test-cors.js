// Test CORS configuration for the new Vercel preview URL
const API_BASE = 'https://bitebase-backend-prod.bitebase.workers.dev';
const VERCEL_ORIGIN = 'https://bitebase-frontend-next-2kprcu5ku-getintheqs-projects.vercel.app';

async function testCORS() {
  console.log('🔍 Testing CORS for Vercel preview URL...\n');
  
  try {
    // Test OPTIONS preflight request
    console.log('Testing OPTIONS preflight request...');
    const optionsResponse = await fetch(`${API_BASE}/health`, {
      method: 'OPTIONS',
      headers: {
        'Origin': VERCEL_ORIGIN,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    
    console.log(`OPTIONS Status: ${optionsResponse.status}`);
    console.log('OPTIONS Headers:');
    for (const [key, value] of optionsResponse.headers.entries()) {
      if (key.toLowerCase().includes('access-control')) {
        console.log(`  ${key}: ${value}`);
      }
    }
    
    // Test actual GET request
    console.log('\nTesting GET request...');
    const getResponse = await fetch(`${API_BASE}/health`, {
      method: 'GET',
      headers: {
        'Origin': VERCEL_ORIGIN
      }
    });
    
    console.log(`GET Status: ${getResponse.status}`);
    console.log('GET Headers:');
    for (const [key, value] of getResponse.headers.entries()) {
      if (key.toLowerCase().includes('access-control')) {
        console.log(`  ${key}: ${value}`);
      }
    }
    
    if (getResponse.ok) {
      const data = await getResponse.json();
      console.log('Response data:', data);
    }
    
  } catch (error) {
    console.error('❌ Error testing CORS:', error.message);
  }
}

testCORS();
