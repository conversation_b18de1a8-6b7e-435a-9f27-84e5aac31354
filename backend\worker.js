/**
 * Cloudflare Worker entry point for BiteBase Backend
 * Simplified version without Express.js dependencies
 */

// Database helper functions
function generateId() {
  return crypto.randomUUID();
}

// Authentication helper functions
async function hashPassword(password) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password);
  const hash = await crypto.subtle.digest('SHA-256', data);
  return Array.from(new Uint8Array(hash))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

async function verifyPassword(password, hashedPassword) {
  const hashedInput = await hashPassword(password);
  return hashedInput === hashedPassword;
}

function generateJWT(payload, secret, expiresIn = '24h') {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const now = Math.floor(Date.now() / 1000);
  const exp = expiresIn === '24h' ? now + (24 * 60 * 60) :
             expiresIn === '7d' ? now + (7 * 24 * 60 * 60) :
             now + 3600; // 1 hour default

  const jwtPayload = {
    ...payload,
    iat: now,
    exp: exp
  };

  const encodedHeader = btoa(JSON.stringify(header)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
  const encodedPayload = btoa(JSON.stringify(jwtPayload)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');

  const signature = btoa(`${encodedHeader}.${encodedPayload}.${secret}`).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');

  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

function generateVerificationToken() {
  return crypto.randomUUID().replace(/-/g, '');
}

function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function validatePassword(password) {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
}

// User management functions
async function createUser(db, userData) {
  const userId = generateId();
  const hashedPassword = await hashPassword(userData.password);
  const verificationToken = generateVerificationToken();

  const user = {
    id: userId,
    email: userData.email.toLowerCase(),
    password_hash: hashedPassword,
    first_name: userData.firstName || userData.first_name || '',
    last_name: userData.lastName || userData.last_name || '',
    phone: userData.phone || '',
    company: userData.company || '',
    role: userData.role || 'user',
    is_verified: false,
    verification_token: verificationToken,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    last_login: null,
    is_active: true
  };

  try {
    // Try with all columns first
    try {
      await db.prepare(`
        INSERT INTO users (
          id, email, password_hash, first_name, last_name, phone, company,
          role, is_verified, verification_token, created_at, updated_at,
          last_login, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        user.id, user.email, user.password_hash, user.first_name, user.last_name,
        user.phone, user.company, user.role, user.is_verified, user.verification_token,
        user.created_at, user.updated_at, user.last_login, user.is_active
      ).run();
    } catch (columnError) {
      // Fallback for missing columns - use basic schema
      if (columnError.message.includes('no column named')) {
        await db.prepare(`
          INSERT INTO users (
            id, email, password_hash, role, is_active, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `).bind(
          user.id, user.email, user.password_hash, user.role, user.is_active,
          user.created_at, user.updated_at
        ).run();
      } else {
        throw columnError;
      }
    }

    return { ...user, password_hash: undefined }; // Don't return password hash
  } catch (error) {
    if (error.message.includes('UNIQUE constraint failed')) {
      throw new Error('User with this email already exists');
    }
    throw error;
  }
}

async function getUserByEmail(db, email) {
  try {
    const result = await db.prepare('SELECT * FROM users WHERE email = ? AND is_active = 1')
      .bind(email.toLowerCase()).first();
    return result;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
}

async function getUserById(db, userId) {
  try {
    const result = await db.prepare('SELECT * FROM users WHERE id = ? AND is_active = 1')
      .bind(userId).first();
    return result;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
}

async function updateUserLastLogin(db, userId) {
  try {
    await db.prepare('UPDATE users SET last_login = ? WHERE id = ?')
      .bind(new Date().toISOString(), userId).run();
  } catch (error) {
    console.error('Error updating last login:', error);
  }
}

async function verifyUserEmail(db, verificationToken) {
  try {
    const result = await db.prepare(`
      UPDATE users SET is_verified = 1, verification_token = NULL, updated_at = ?
      WHERE verification_token = ? AND is_active = 1
    `).bind(new Date().toISOString(), verificationToken).run();

    return result.changes > 0;
  } catch (error) {
    console.error('Error verifying email:', error);
    return false;
  }
}

// Google OAuth verification
async function verifyGoogleToken(token) {
  try {
    const response = await fetch(`https://oauth2.googleapis.com/tokeninfo?id_token=${token}`);
    if (!response.ok) {
      return null;
    }

    const data = await response.json();

    // Verify the token is valid and not expired
    if (data.aud && data.exp && parseInt(data.exp) > Date.now() / 1000) {
      return {
        email: data.email,
        firstName: data.given_name || '',
        lastName: data.family_name || '',
        name: data.name || '',
        picture: data.picture || '',
        verified: data.email_verified === 'true'
      };
    }

    return null;
  } catch (error) {
    console.error('Error verifying Google token:', error);
    return null;
  }
}

// Email sending function (placeholder - would integrate with email service)
async function sendVerificationEmail(email, verificationToken, frontendUrl) {
  // In a real implementation, this would integrate with an email service like SendGrid, Mailgun, etc.
  const verificationUrl = `${frontendUrl}/auth/verify-email?token=${verificationToken}`;

  console.log(`Verification email would be sent to ${email} with URL: ${verificationUrl}`);

  // For now, return success. In production, implement actual email sending
  return {
    success: true,
    message: 'Verification email sent successfully'
  };
}

// Distance calculation using Haversine formula
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

// Foursquare API integration
async function searchFoursquareVenues({ lat, lng, radius = 1000, limit = 20, apiKey }) {
  const url = 'https://api.foursquare.com/v3/places/search';
  const params = new URLSearchParams({
    ll: `${lat},${lng}`,
    radius: radius.toString(),
    categories: '13000', // Food and dining category
    limit: Math.min(limit, 50).toString(),
    fields: 'fsq_id,name,location,categories,rating,price,hours,website,tel,distance'
  });

  const response = await fetch(`${url}?${params}`, {
    headers: {
      'Authorization': apiKey,
      'Accept': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`Foursquare API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  
  // Transform Foursquare data to our format
  return (data.results || []).map(venue => ({
    id: venue.fsq_id,
    name: venue.name,
    latitude: venue.location?.latitude || lat,
    longitude: venue.location?.longitude || lng,
    address: venue.location?.formatted_address || venue.location?.address,
    cuisine_types: venue.categories?.map(cat => cat.name) || ['Restaurant'],
    price_range: venue.price || 2,
    rating: venue.rating || 4.0,
    distance_km: venue.distance ? (venue.distance / 1000) : calculateDistance(lat, lng, venue.location?.latitude || lat, venue.location?.longitude || lng),
    phone: venue.tel,
    website: venue.website,
    platform: 'foursquare',
    platform_id: venue.fsq_id,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }));
}

// Language detection for natural responses
function detectLanguage(text) {
  const thaiPattern = /[\u0E00-\u0E7F]/;
  const thaiWords = ['สวัสดี', 'ครับ', 'ค่ะ', 'ขอบคุณ', 'ร้าน', 'อาหาร', 'ลูกค้า', 'รายได้', 'ยอดขาย'];
  
  if (thaiPattern.test(text)) return 'th';
  
  const lowerText = text.toLowerCase();
  const hasThaiWords = thaiWords.some(word => lowerText.includes(word));
  
  return hasThaiWords ? 'th' : 'en';
}

// Intent detection for contextual responses
function determineIntent(message) {
  const lowerMessage = message.toLowerCase().trim();

  // Greetings
  if (lowerMessage === 'สวัสดี' || lowerMessage === 'hello' || lowerMessage === 'hi' ||
      lowerMessage === 'hey' || lowerMessage === 'สวัสดีครับ' || lowerMessage === 'สวัสดีค่ะ') {
    return 'greeting';
  }

  // Business questions
  if (lowerMessage.includes('sales') || lowerMessage.includes('revenue') ||
      lowerMessage.includes('รายได้') || lowerMessage.includes('ยอดขาย')) {
    return 'sales_analysis';
  }

  if (lowerMessage.includes('customer') || lowerMessage.includes('ลูกค้า')) {
    return 'customer_analysis';
  }

  if (lowerMessage.includes('marketing') || lowerMessage.includes('การตลาด')) {
    return 'marketing_advice';
  }

  return 'general_help';
}

// Generate human-like responses with personality
function generateHumanLikeResponse(message, language, intent) {
  if (language === 'th') {
    return generateThaiResponse(message, intent);
  } else {
    return generateEnglishResponse(message, intent);
  }
}

// Natural Thai responses with personality
function generateThaiResponse(message, intent) {
  switch (intent) {
    case 'greeting':
      return {
        content: `สวัสดีครับ! ผมอเล็กซ์ครับ ยินดีที่ได้รู้จักนะครับ!

ผมเป็นที่ปรึกษาธุรกิจร้านอาหารที่มีประสบการณ์มากว่า 15 ปีแล้วครับ ผมรู้ดีว่าการทำธุรกิจร้านอาหารมันไม่ง่าย แต่ผมพร้อมช่วยให้คุณประสบความสำเร็จครับ!

ความสามารถพิเศษของผม:
• การวิเคราะห์รายได้เชิงลึก
• กลยุทธ์การตลาดที่ได้ผลจริง
• การวิเคราะห์คู่แข่งแบบเรียลไทม์
• ความเข้าใจลูกค้าลึกซึ้ง
• การพยากรณ์อนาคต
• คำแนะนำอัจฉริยะ

เล่าให้ผมฟังหน่อยครับว่าวันนี้มีอะไรที่อยากปรึกษา? ผมพร้อมวิเคราะห์และให้คำแนะนำที่แม่นยำครับ!`,
        suggestions: ['วิเคราะห์รายได้ร้าน', 'แนะนำกลยุทธ์การตลาด', 'พยากรณ์อนาคตธุรกิจ']
      };

    case 'sales_analysis':
      return {
        content: `เยี่ยมมาก! คำถามเรื่องรายได้และยอดขายนี่สำคัญมากเลยครับ

จากประสบการณ์ที่ผมช่วยร้านอาหารมาเยอะ การวิเคราะห์รายได้ที่ดีต้องดูหลายมิติครับ:

มิติรายได้หลัก:
• รายได้รายวัน/สัปดาห์/เดือน
• ค่าเฉลี่ยต่อลูกค้า (Average Order Value)
• จำนวนลูกค้าต่อวัน
• เมนูที่ขายดีที่สุด

การเพิ่มยอดขาย:
• ปรับเมนูให้น่าสนใจมากขึ้น
• โปรโมชั่นที่เหมาะสมกับช่วงเวลา
• ปรับปรุงการบริการ
• ใช้ Social Media ให้เป็นประโยชน์

อยากให้ผมช่วยวิเคราะห์ข้อมูลร้านของคุณเพิ่มเติมไหมครับ?`,
        suggestions: ['ดูข้อมูลรายได้รายเดือน', 'วิเคราะห์เมนูขายดี', 'แนะนำโปรโมชั่น']
      };

    case 'customer_analysis':
      return {
        content: `ดีมากเลยครับ! การเข้าใจลูกค้าคือกุญแจสำคัญของความสำเร็จ

จากที่ผมเห็นมาหลายร้าน ลูกค้าแต่ละกลุ่มมีพฤติกรรมไม่เหมือนกันครับ:

ประเภทลูกค้าหลัก:
• ลูกค้าประจำ (มาบ่อย, สั่งเมนูคุ้นเคย)
• ลูกค้าใหม่ (อยากลองของใหม่)
• ลูกค้าผ่านทาง (มาแค่ครั้งเดียว)
• ลูกค้ากลุ่ม (มาเป็นครอบครัว/เพื่อน)

วิธีดึงดูดและรักษาลูกค้า:
• สร้างความประทับใจครั้งแรก
• มีโปรสำหรับลูกค้าประจำ
• บริการที่อบอุ่นและเป็นมิตร
• คุณภาพอาหารที่สม่ำเสมอ

อยากรู้เรื่องลูกค้าเฉพาะด้านไหนเพิ่มเติมครับ?`,
        suggestions: ['วิธีรักษาลูกค้าประจำ', 'หาลูกค้าใหม่', 'เพิ่มความพึงพอใจ']
      };

    default:
      return {
        content: `ขอบคุณสำหรับคำถามครับ!

ผมอเล็กซ์ ที่ปรึกษาธุรกิจร้านอาหารที่พร้อมช่วยคุณในทุกเรื่องครับ ไม่ว่าจะเป็น:

บริการหลักของผม:
• การวิเคราะห์ข้อมูลธุรกิจอย่างละเอียด
• วางแผนกลยุทธ์การตลาด
• หาทางเพิ่มรายได้และลดต้นทุน
• ศึกษาคู่แข่งและหาจุดเด่น
• เข้าใจและดูแลลูกค้าให้ดีขึ้น

คุณอยากให้ผมช่วยเรื่องไหนเป็นพิเศษครับ? บอกมาได้เลย ผมพร้อมให้คำแนะนำที่เป็นประโยชน์ครับ!`,
        suggestions: ['วิเคราะห์ธุรกิจ', 'แนะนำกลยุทธ์', 'หาจุดแข็งร้าน']
      };
  }
}

// Natural English responses with personality
function generateEnglishResponse(message, intent) {
  switch (intent) {
    case 'greeting':
      return {
        content: `Hi there! I'm Alex, and I'm absolutely delighted to meet you!

I'm a restaurant business consultant with over 15 years of experience in the food industry. I know firsthand how challenging and rewarding this business can be, and I'm here to help you succeed!

Here's how I can help you:
• Deep revenue and sales analysis
• Effective marketing strategies that actually work
• Competitive analysis and market insights
• Better understanding of customer behavior
• Problem-solving and profit optimization

What's on your mind today? I'm here to help with whatever challenges you're facing!`,
        suggestions: ['Analyze my revenue', 'Get marketing advice', 'Study competitors']
      };

    case 'sales_analysis':
      return {
        content: `Fantastic question! Revenue and sales analysis is absolutely crucial for success.

From helping hundreds of restaurants, I've learned that good revenue analysis needs to look at multiple dimensions:

Key Revenue Metrics:
• Daily/weekly/monthly revenue trends
• Average Order Value (AOV)
• Customer count per day
• Best-selling menu items
• Peak vs. slow periods

Boosting Sales Strategies:
• Menu optimization and pricing
• Strategic promotions and timing
• Service quality improvements
• Smart social media marketing
• Customer loyalty programs

Would you like me to dive deeper into any specific aspect of your restaurant's revenue? I'm excited to help you grow!`,
        suggestions: ['Monthly revenue trends', 'Analyze best sellers', 'Suggest promotions']
      };

    case 'customer_analysis':
      return {
        content: `Excellent! Understanding your customers is the key to restaurant success.

From my experience working with restaurants, I've seen how different customer segments behave:

Main Customer Types:
• Regular customers (frequent visits, familiar orders)
• New customers (curious about trying new things)
• Walk-ins (one-time visitors)
• Group diners (families, friends, celebrations)

Customer Retention & Attraction:
• Create amazing first impressions
• Reward loyal customers with special offers
• Provide warm, friendly service
• Maintain consistent food quality
• Build community connections

What specific aspect of customer behavior would you like to explore? I'm here to help you build stronger relationships!`,
        suggestions: ['Retain regular customers', 'Attract new diners', 'Improve satisfaction']
      };

    default:
      return {
        content: `Thanks for reaching out!

I'm Alex, your friendly restaurant business consultant, and I'm here to help with whatever you need! Whether it's:

My core services:
• Detailed business data analysis
• Strategic marketing planning
• Revenue optimization and cost reduction
• Competitive analysis and positioning
• Customer experience enhancement

What would you like to focus on today? I'm excited to provide insights that will help your restaurant thrive! Just let me know what's most important to you right now.`,
        suggestions: ['Business analysis', 'Strategy advice', 'Find strengths']
      };
  }
}

// Helper function to get allowed CORS origin
function getAllowedOrigin(requestOrigin, env) {
  const allowedOrigins = [
    'https://beta.bitebase.app',
    'http://localhost:12000',
    'http://localhost:3000',
    'http://127.0.0.1:12000',
    'http://127.0.0.1:3000'
  ];

  // Add custom CORS origin from environment if set
  if (env.CORS_ORIGIN) {
    allowedOrigins.push(env.CORS_ORIGIN);
  }

  // Allow Vercel preview URLs
  if (requestOrigin && requestOrigin.includes('.vercel.app')) {
    return requestOrigin;
  }

  // Check if the request origin is in our allowed list
  if (requestOrigin && allowedOrigins.includes(requestOrigin)) {
    return requestOrigin;
  }

  // Default fallback
  return 'https://beta.bitebase.app';
}

// Worker fetch event handler
export default {
  async fetch(request, env, ctx) {
    try {
      // Create a mock Express request/response
      const url = new URL(request.url);
      const origin = request.headers.get('Origin');
      const allowedOrigin = getAllowedOrigin(origin, env);
      
      // Handle CORS preflight requests
      if (request.method === 'OPTIONS') {
        return new Response(null, {
          status: 200,
          headers: {
            'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
          },
        });
      }

      // Health check endpoint
      if (url.pathname === '/health') {
        return new Response(JSON.stringify({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          service: 'bitebase-cloudflare-backend',
          version: '2.0.0',
          environment: env.NODE_ENV || 'production',
          services: {
            api: true,
            database: !!env.DATABASE_URL,
            redis: !!env.REDIS_URL,
            ai: !!env.BEDROCK_API_KEY,
            mapbox: !!env.MAPBOX_API_KEY,
          }
        }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
          },
        });
      }

      // AI status endpoint
      if (url.pathname === '/ai') {
        return new Response(JSON.stringify({
          status: env.BEDROCK_API_KEY ? 'operational' : 'unavailable',
          version: '2.0.0',
          timestamp: new Date().toISOString(),
          features: ['conversational_analytics', 'predictive_insights', 'competitive_intelligence'],
          models: env.BEDROCK_API_KEY ? {
            chat: env.BEDROCK_CHAT_MODEL,
            reasoning: env.BEDROCK_REASONING_MODEL,
            fast: env.BEDROCK_FAST_MODEL,
            gateway_url: env.BEDROCK_API_BASE_URL
          } : null,
          fallback_available: true
        }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
          },
        });
      }

      // User Registration endpoint
      if (url.pathname === '/auth/register' && request.method === 'POST') {
        try {
          const body = await request.json();
          const { email, password, firstName, lastName, phone, company } = body;

          // Validate input
          if (!email || !password) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Email and password are required',
              status: 400
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          if (!validateEmail(email)) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid email format',
              status: 400
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          if (!validatePassword(password)) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Password must be at least 8 characters with uppercase, lowercase, and number',
              status: 400
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          // Check if user already exists
          const existingUser = await getUserByEmail(env.DB, email);
          if (existingUser) {
            return new Response(JSON.stringify({
              success: false,
              message: 'User with this email already exists',
              status: 409
            }), {
              status: 409,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          // Create new user
          const newUser = await createUser(env.DB, {
            email,
            password,
            firstName: firstName || '',
            lastName: lastName || '',
            phone: phone || '',
            company: company || ''
          });

          // Send verification email
          const frontendUrl = env.FRONTEND_URL || 'https://beta.bitebase.app';
          await sendVerificationEmail(email, newUser.verification_token, frontendUrl);

          // Generate JWT token
          const token = generateJWT({
            userId: newUser.id,
            email: newUser.email,
            role: newUser.role
          }, env.JWT_SECRET || 'default-secret');

          return new Response(JSON.stringify({
            success: true,
            message: 'User registered successfully. Please check your email for verification.',
            data: {
              user: {
                id: newUser.id,
                email: newUser.email,
                firstName: newUser.first_name,
                lastName: newUser.last_name,
                phone: newUser.phone,
                company: newUser.company,
                role: newUser.role,
                isVerified: newUser.is_verified
              },
              token: token,
              refresh_token: generateJWT({ userId: newUser.id }, env.JWT_SECRET || 'default-secret', '7d')
            },
            timestamp: new Date().toISOString(),
            status: 201
          }), {
            status: 201,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
              'Access-Control-Allow-Credentials': 'true',
            },
          });

        } catch (error) {
          console.error('Registration error:', error);
          return new Response(JSON.stringify({
            success: false,
            message: error.message || 'Registration failed',
            status: 500
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
              'Access-Control-Allow-Credentials': 'true',
            },
          });
        }
      }

      // User Login endpoint
      if (url.pathname === '/auth/login' && request.method === 'POST') {
        try {
          const body = await request.json();
          const { email, password } = body;

          // Validate input
          if (!email || !password) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Email and password are required',
              status: 400
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          // Get user by email
          const user = await getUserByEmail(env.DB, email);
          if (!user) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid email or password',
              status: 401
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          // Verify password
          const isValidPassword = await verifyPassword(password, user.password_hash);
          if (!isValidPassword) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid email or password',
              status: 401
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          // Update last login
          await updateUserLastLogin(env.DB, user.id);

          // Generate JWT token
          const token = generateJWT({
            userId: user.id,
            email: user.email,
            role: user.role
          }, env.JWT_SECRET || 'default-secret');

          return new Response(JSON.stringify({
            success: true,
            message: 'Login successful',
            data: {
              user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone,
                company: user.company,
                role: user.role,
                isVerified: user.is_verified
              },
              token: token,
              refresh_token: generateJWT({ userId: user.id }, env.JWT_SECRET || 'default-secret', '7d')
            },
            timestamp: new Date().toISOString(),
            status: 200
          }), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
              'Access-Control-Allow-Credentials': 'true',
            },
          });

        } catch (error) {
          console.error('Login error:', error);
          return new Response(JSON.stringify({
            success: false,
            message: 'Login failed',
            status: 500
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
              'Access-Control-Allow-Credentials': 'true',
            },
          });
        }
      }

      // Admin authentication endpoint
      if (url.pathname === '/auth/admin' && request.method === 'POST') {
        try {
          const body = await request.json();
          const { email, password } = body;

          if (email === '<EMAIL>' && password === 'Libralytics1234!*') {
            return new Response(JSON.stringify({
              success: true,
              message: 'Admin authentication successful',
              data: {
                user: {
                  id: 'admin',
                  email: '<EMAIL>',
                  firstName: 'Admin',
                  lastName: 'User',
                  role: 'admin',
                  isAdmin: true
                },
                token: 'admin-token-' + Date.now()
              },
              timestamp: new Date().toISOString(),
              status: 200
            }), {
              status: 200,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
              },
            });
          } else {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid admin credentials',
              timestamp: new Date().toISOString(),
              status: 401
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
              },
            });
          }
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Invalid request body',
            timestamp: new Date().toISOString(),
            status: 400
          }), {
            status: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
            },
          });
        }
      }

      // Google OAuth authentication endpoint
      if (url.pathname === '/auth/google' && request.method === 'POST') {
        try {
          const body = await request.json();
          const { token } = body;

          if (!token) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Google token is required',
              status: 400
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          // Verify Google token
          const googleUser = await verifyGoogleToken(token);
          if (!googleUser) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid Google token',
              status: 401
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          // Check if user exists
          let user = await getUserByEmail(env.DB, googleUser.email);
          let isNewUser = false;

          if (!user) {
            // Create new user from Google data
            isNewUser = true;
            user = await createUser(env.DB, {
              email: googleUser.email,
              password: crypto.randomUUID(), // Random password for OAuth users
              firstName: googleUser.firstName,
              lastName: googleUser.lastName,
              role: 'user'
            });

            // Mark as verified since Google verified the email
            await verifyUserEmail(env.DB, user.verification_token);
            user.is_verified = true;
          } else {
            // Update last login for existing user
            await updateUserLastLogin(env.DB, user.id);
          }

          // Generate JWT token
          const jwtToken = generateJWT({
            userId: user.id,
            email: user.email,
            role: user.role
          }, env.JWT_SECRET || 'default-secret');

          return new Response(JSON.stringify({
            success: true,
            message: isNewUser ? 'Account created successfully' : 'Login successful',
            data: {
              user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone || '',
                company: user.company || '',
                role: user.role,
                isVerified: user.is_verified
              },
              token: jwtToken,
              refresh_token: generateJWT({ userId: user.id }, env.JWT_SECRET || 'default-secret', '7d'),
              isNewUser: isNewUser
            },
            timestamp: new Date().toISOString(),
            status: 200
          }), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
              'Access-Control-Allow-Credentials': 'true',
            },
          });

        } catch (error) {
          console.error('Google OAuth error:', error);
          return new Response(JSON.stringify({
            success: false,
            message: 'Google authentication failed',
            status: 500
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
              'Access-Control-Allow-Credentials': 'true',
            },
          });
        }
      }

      // Email verification endpoint
      if (url.pathname === '/auth/verify-email' && request.method === 'POST') {
        try {
          const body = await request.json();
          const { token } = body;

          if (!token) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Verification token is required',
              status: 400
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          // Verify email with token
          const verified = await verifyUserEmail(env.DB, token);
          if (!verified) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid or expired verification token',
              status: 400
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          return new Response(JSON.stringify({
            success: true,
            message: 'Email verified successfully',
            timestamp: new Date().toISOString(),
            status: 200
          }), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
              'Access-Control-Allow-Credentials': 'true',
            },
          });

        } catch (error) {
          console.error('Email verification error:', error);
          return new Response(JSON.stringify({
            success: false,
            message: 'Email verification failed',
            status: 500
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
              'Access-Control-Allow-Credentials': 'true',
            },
          });
        }
      }

      // AI chat endpoint with human-like responses
      if (url.pathname === '/ai/chat' && request.method === 'POST') {
        try {
          const body = await request.json();
          const { message, conversation_id } = body;

          if (!message) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Message is required',
              timestamp: new Date().toISOString(),
              status: 400
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          // Detect language and intent
          const language = detectLanguage(message);
          const intent = determineIntent(message);

          // Generate natural, human-like response
          const response = generateHumanLikeResponse(message, language, intent);

          const aiResponse = {
            success: true,
            message: 'AI response generated successfully',
            data: {
              response: response.content,
              intent: intent,
              language: language,
              suggestions: response.suggestions,
              model: 'alex-ai-consultant',
              data_source: 'bitebase-ai',
              conversation_id: conversation_id || crypto.randomUUID()
            },
            timestamp: new Date().toISOString(),
            status: 200
          };

          return new Response(JSON.stringify(aiResponse), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
            },
          });

        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to process AI request',
            error: error.message,
            timestamp: new Date().toISOString(),
            status: 500
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
            },
          });
        }
      }

      // Admin metrics endpoint
      if (url.pathname === '/api/admin/metrics' && request.method === 'GET') {
        return new Response(JSON.stringify({
          users: {
            total: 1247,
            active: 892,
            newThisMonth: 89,
            churnRate: 0.045
          },
          revenue: {
            mrr: 24500,
            arr: 294000,
            growth: 0.125
          },
          system: {
            status: 'healthy',
            uptime: 0.996,
            responseTime: 145,
            errorRate: 0.002
          },
          subscriptions: {
            starter: 45,
            professional: 28,
            enterprise: 12
          }
        }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
          },
        });
      }

      // Restaurant search endpoint
      if (url.pathname === '/restaurants/search' && request.method === 'POST') {
        try {
          const body = await request.json();
          const { latitude, longitude, location, radius = 5, limit = 20 } = body;
          
          // Support both formats: direct lat/lng or location object
          const lat = latitude || location?.latitude;
          const lng = longitude || location?.longitude;
          
          if (!lat || !lng) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Location coordinates are required (latitude and longitude)',
              timestamp: new Date().toISOString(),
              status: 400
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          // Try Foursquare API first if available
          let restaurants = [];
          let dataSource = 'database';
          
          if (env.FOURSQUARE_API_KEY) {
            try {
              console.log('🔍 Searching Foursquare API for restaurants');
              const foursquareResponse = await searchFoursquareVenues({
                lat,
                lng,
                radius: radius * 1000, // Convert km to meters
                limit,
                apiKey: env.FOURSQUARE_API_KEY
              });
              
              if (foursquareResponse && foursquareResponse.length > 0) {
                restaurants = foursquareResponse;
                dataSource = 'foursquare';
                console.log(`✅ Found ${restaurants.length} restaurants from Foursquare`);
              }
            } catch (error) {
              console.error('❌ Foursquare API error:', error.message);
            }
          }
          
          // Fallback to database if no Foursquare results
          if (restaurants.length === 0) {
            console.log('🔄 Falling back to database search');
            const query = `
              SELECT id, name, latitude, longitude, cuisine_types, price_range, rating, 
                     description, business_hours, created_at, updated_at
              FROM restaurants 
              WHERE is_active = 1
              ORDER BY 
                (latitude - ?) * (latitude - ?) + (longitude - ?) * (longitude - ?)
              LIMIT ?
            `;

            const { results } = await env.DB.prepare(query)
              .bind(lat, lat, lng, lng, limit)
              .all();

            // Calculate distances and format results
            restaurants = results.map(restaurant => {
              const distance_km = calculateDistance(
                lat, lng, 
                restaurant.latitude, restaurant.longitude
              );

              return {
                id: restaurant.id,
                name: restaurant.name,
                cuisine_types: JSON.parse(restaurant.cuisine_types || '[]'),
                price_range: restaurant.price_range,
                rating: restaurant.rating,
                location: { 
                  latitude: restaurant.latitude, 
                  longitude: restaurant.longitude 
                },
                distance_km: Math.round(distance_km * 100) / 100,
                description: restaurant.description,
                business_hours: JSON.parse(restaurant.business_hours || '{}'),
                created_at: restaurant.created_at,
                updated_at: restaurant.updated_at
              };
            }).filter(restaurant => restaurant.distance_km <= radius);
          }

          return new Response(JSON.stringify({
            success: true,
            data: {
              restaurants,
              search_params: { latitude, longitude, radius, limit },
              total_found: restaurants.length,
              data_source: dataSource,
              generated_at: new Date().toISOString()
            },
            timestamp: new Date().toISOString(),
            status: 200
          }), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
            },
          });
        } catch (error) {
          console.error('Restaurant search error:', error);
          return new Response(JSON.stringify({
            success: false,
            message: 'Error searching restaurants: ' + error.message,
            timestamp: new Date().toISOString(),
            status: 500
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
            },
          });
        }
      }

      // Restaurant featured endpoint
      if (url.pathname === '/restaurants/featured' && request.method === 'GET') {
        try {
          // Get top-rated restaurants from database
          const query = `
            SELECT id, name, latitude, longitude, cuisine_types, price_range, rating, 
                   description, business_hours, created_at, updated_at
            FROM restaurants 
            WHERE is_active = 1 AND rating >= 4.5
            ORDER BY rating DESC, total_reviews DESC
            LIMIT 10
          `;

          const { results } = await env.DB.prepare(query).all();

          const featuredRestaurants = results.map(restaurant => ({
            id: restaurant.id,
            name: restaurant.name,
            cuisine_types: JSON.parse(restaurant.cuisine_types || '[]'),
            price_range: restaurant.price_range,
            rating: restaurant.rating,
            location: { 
              latitude: restaurant.latitude, 
              longitude: restaurant.longitude 
            },
            description: restaurant.description,
            business_hours: JSON.parse(restaurant.business_hours || '{}'),
            featured_reason: restaurant.rating >= 4.8 ? 'Exceptional Rating' : 'Highly Rated',
            created_at: restaurant.created_at,
            updated_at: restaurant.updated_at
          }));

          return new Response(JSON.stringify({
            success: true,
            data: featuredRestaurants,
            timestamp: new Date().toISOString(),
            status: 200
          }), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
            },
          });
        } catch (error) {
          console.error('Featured restaurants error:', error);
          return new Response(JSON.stringify({
            success: false,
            message: 'Error fetching featured restaurants: ' + error.message,
            timestamp: new Date().toISOString(),
            status: 500
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
            },
          });
        }
      }

      // Dedicated Foursquare search endpoint
      if (url.pathname === '/foursquare/search' && request.method === 'POST') {
        try {
          const body = await request.json();
          const { latitude, longitude, radius = 1, limit = 20, categories } = body;
          
          if (!latitude || !longitude) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Location coordinates are required (latitude and longitude)',
              timestamp: new Date().toISOString(),
              status: 400
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          if (!env.FOURSQUARE_API_KEY) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Foursquare API not configured',
              timestamp: new Date().toISOString(),
              status: 503
            }), {
              status: 503,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          console.log('🔍 Direct Foursquare API search');
          const venues = await searchFoursquareVenues({
            lat: latitude,
            lng: longitude,
            radius: radius * 1000, // Convert km to meters
            limit,
            apiKey: env.FOURSQUARE_API_KEY
          });

          return new Response(JSON.stringify({
            success: true,
            data: {
              venues,
              search_params: { latitude, longitude, radius, limit },
              total_found: venues.length,
              data_source: 'foursquare',
              generated_at: new Date().toISOString()
            },
            timestamp: new Date().toISOString(),
            status: 200
          }), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
              'Access-Control-Allow-Credentials': 'true',
            },
          });
        } catch (error) {
          console.error('Foursquare search error:', error);
          return new Response(JSON.stringify({
            success: false,
            message: 'Error searching Foursquare: ' + error.message,
            timestamp: new Date().toISOString(),
            status: 500
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
              'Access-Control-Allow-Credentials': 'true',
            },
          });
        }
      }

      // Database initialization endpoint
      if (url.pathname === '/admin/init-db' && request.method === 'POST') {
        try {
          // Create restaurants table
          await env.DB.prepare(`CREATE TABLE IF NOT EXISTS restaurants (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            latitude REAL,
            longitude REAL,
            cuisine_types TEXT,
            price_range INTEGER CHECK (price_range BETWEEN 1 AND 4),
            rating REAL CHECK (rating >= 0 AND rating <= 5),
            total_reviews INTEGER DEFAULT 0,
            phone TEXT,
            email TEXT,
            website TEXT,
            description TEXT,
            business_hours TEXT,
            amenities TEXT,
            created_at TEXT DEFAULT (datetime('now')),
            updated_at TEXT DEFAULT (datetime('now')),
            is_active INTEGER DEFAULT 1
          )`).run();

          // Insert sample restaurants
          await env.DB.prepare(`INSERT OR REPLACE INTO restaurants (id, name, latitude, longitude, cuisine_types, price_range, rating, description, business_hours) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`).bind(
            'rest_demo_001', 'Gaggan Progressive Indian', 13.7300, 100.5400, '["Indian", "Progressive", "Fine Dining"]', 4, 4.8, 'World-renowned progressive Indian cuisine restaurant in Bangkok', '{"monday": "18:00-23:00", "tuesday": "18:00-23:00", "wednesday": "18:00-23:00", "thursday": "18:00-23:00", "friday": "18:00-23:00", "saturday": "18:00-23:00", "sunday": "closed"}'
          ).run();

          await env.DB.prepare(`INSERT OR REPLACE INTO restaurants (id, name, latitude, longitude, cuisine_types, price_range, rating, description, business_hours) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`).bind(
            'rest_demo_002', 'Jay Fai', 13.7600, 100.5100, '["Thai", "Street Food"]', 2, 4.7, 'Michelin-starred street food stall famous for crab omelets', '{"monday": "14:00-19:00", "tuesday": "14:00-19:00", "wednesday": "closed", "thursday": "14:00-19:00", "friday": "14:00-19:00", "saturday": "14:00-19:00", "sunday": "14:00-19:00"}'
          ).run();

          await env.DB.prepare(`INSERT OR REPLACE INTO restaurants (id, name, latitude, longitude, cuisine_types, price_range, rating, description, business_hours) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`).bind(
            'rest_demo_003', 'Thip Samai Pad Thai', 13.7563, 100.5018, '["Thai", "Street Food", "Noodles"]', 1, 4.4, 'Famous for the best Pad Thai in Bangkok since 1966', '{"monday": "17:00-02:00", "tuesday": "17:00-02:00", "wednesday": "17:00-02:00", "thursday": "17:00-02:00", "friday": "17:00-02:00", "saturday": "17:00-02:00", "sunday": "17:00-02:00"}'
          ).run();

          return new Response(JSON.stringify({
            success: true,
            message: 'Database initialized successfully with sample restaurants',
            data: {
              restaurants_created: 3,
              sample_restaurants: [
                'Gaggan Progressive Indian',
                'Jay Fai', 
                'Thip Samai Pad Thai'
              ]
            },
            timestamp: new Date().toISOString(),
            status: 200
          }), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
            },
          });

        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            message: 'Database initialization failed: ' + error.message,
            timestamp: new Date().toISOString(),
            status: 500
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
            'Access-Control-Allow-Credentials': 'true',
            },
          });
        }
      }

      // Refresh token endpoint
      if (url.pathname === '/auth/refresh-token' && request.method === 'POST') {
        try {
          const body = await request.json();
          const { refresh_token } = body;

          if (!refresh_token) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Refresh token is required',
              status: 400
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

          // In a real implementation, you would verify the refresh token
          // For now, we'll extract the userId from the token (simplified)
          try {
            const tokenParts = refresh_token.split('.');
            if (tokenParts.length !== 3) {
              throw new Error('Invalid token format');
            }

            const payload = JSON.parse(atob(tokenParts[1]));
            const userId = payload.userId;

            if (!userId) {
              throw new Error('Invalid token payload');
            }

            // Get user to generate new token
            const user = await getUserById(env.DB, userId);
            if (!user) {
              throw new Error('User not found');
            }

            // Generate new access token
            const newToken = generateJWT({
              userId: user.id,
              email: user.email,
              role: user.role
            }, env.JWT_SECRET || 'default-secret');

            return new Response(JSON.stringify({
              success: true,
              message: 'Token refreshed successfully',
              data: {
                token: newToken,
                refresh_token: generateJWT({ userId: user.id }, env.JWT_SECRET || 'default-secret', '7d')
              },
              timestamp: new Date().toISOString(),
              status: 200
            }), {
              status: 200,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });

          } catch (tokenError) {
            return new Response(JSON.stringify({
              success: false,
              message: 'Invalid refresh token',
              status: 401
            }), {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': allowedOrigin,
                'Access-Control-Allow-Credentials': 'true',
              },
            });
          }

        } catch (error) {
          console.error('Token refresh error:', error);
          return new Response(JSON.stringify({
            success: false,
            message: 'Token refresh failed',
            status: 500
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': allowedOrigin,
              'Access-Control-Allow-Credentials': 'true',
            },
          });
        }
      }

      // Default response for other endpoints
      return new Response(JSON.stringify({
        message: 'BiteBase AI-Powered Backend',
        version: '2.0.0',
        timestamp: new Date().toISOString(),
        available_endpoints: [
          '/health',
          '/ai',
          '/ai/chat (Natural AI Assistant)',
          '/auth/register (POST)',
          '/auth/login (POST)',
          '/auth/google (POST)',
          '/auth/verify-email (POST)',
          '/auth/refresh-token (POST)',
          '/auth/admin (POST)',
          '/api/admin/metrics',
          '/restaurants/search (GET/POST)',
          '/restaurants/search/realtime',
          '/restaurants/featured',
          '/restaurants/{id}',
          '/api/restaurants',
          '/api/restaurants/{id}/analytics',
          '/foursquare/search',
          '/user/location/update'
        ]
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': allowedOrigin,
        },
      });

    } catch (error) {
      console.error('Worker error:', error);
      
      return new Response(JSON.stringify({
        success: false,
        message: 'Internal server error',
        error: error.message,
        timestamp: new Date().toISOString(),
        status: 500
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': allowedOrigin,
        },
      });
    }
  },
};