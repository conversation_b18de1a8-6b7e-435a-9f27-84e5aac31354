-- Migration to add authentication columns to existing users table
-- This adds the missing columns needed for the new authentication system

-- Add missing columns to users table
ALTER TABLE users ADD COLUMN first_name TEXT;
ALTER TABLE users ADD COLUMN last_name TEXT;
ALTER TABLE users ADD COLUMN company TEXT;
ALTER TABLE users ADD COLUMN is_verified INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN verification_token TEXT;

-- Update existing users to have proper structure
UPDATE users SET 
    first_name = CASE 
        WHEN full_name IS NOT NULL THEN 
            CASE 
                WHEN instr(full_name, ' ') > 0 THEN substr(full_name, 1, instr(full_name, ' ') - 1)
                ELSE full_name
            END
        ELSE ''
    END,
    last_name = CASE 
        WHEN full_name IS NOT NULL AND instr(full_name, ' ') > 0 THEN 
            substr(full_name, instr(full_name, ' ') + 1)
        ELSE ''
    END,
    company = '',
    is_verified = email_verified,
    verification_token = NULL
WHERE first_name IS NULL;

-- Update role for existing admin user
UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';

-- Ensure admin user is verified
UPDATE users SET is_verified = 1 WHERE email = '<EMAIL>';
