// Test script to verify all authentication endpoints
const API_BASE = 'https://bitebase-backend-prod.bitebase.workers.dev';
const ORIGIN = 'https://bitebase-frontend-next-2kprcu5ku-getintheqs-projects.vercel.app';

async function testEndpoint(endpoint, method, body = null) {
  try {
    console.log(`\n🔍 Testing ${method} ${endpoint}`);
    
    const options = {
      method,
      headers: {
        'Origin': ORIGIN,
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(`${API_BASE}${endpoint}`, options);
    const data = await response.json();
    
    console.log(`Status: ${response.status}`);
    console.log(`Response:`, data);
    
    return { status: response.status, data };
  } catch (error) {
    console.error(`❌ Error testing ${endpoint}:`, error.message);
    return { error: error.message };
  }
}

async function runTests() {
  console.log('🚀 Testing BiteBase Authentication Endpoints\n');
  
  // Test 1: Check available endpoints
  await testEndpoint('/', 'GET');
  
  // Test 2: Test user registration
  const testUser = {
    email: '<EMAIL>',
    password: 'TestPassword123',
    firstName: 'Test',
    lastName: 'User',
    phone: '+1234567890',
    company: 'Test Company'
  };
  
  await testEndpoint('/auth/register', 'POST', testUser);
  
  // Test 3: Test user login
  await testEndpoint('/auth/login', 'POST', {
    email: testUser.email,
    password: testUser.password
  });
  
  // Test 4: Test admin login
  await testEndpoint('/auth/admin', 'POST', {
    email: '<EMAIL>',
    password: 'Libralytics1234!*'
  });
  
  // Test 5: Test Google OAuth (with invalid token)
  await testEndpoint('/auth/google', 'POST', {
    token: 'invalid_google_token'
  });
  
  // Test 6: Test email verification (with invalid token)
  await testEndpoint('/auth/verify-email', 'POST', {
    token: 'invalid_verification_token'
  });
  
  // Test 7: Test refresh token (with invalid token)
  await testEndpoint('/auth/refresh-token', 'POST', {
    refresh_token: 'invalid_refresh_token'
  });
  
  console.log('\n✅ Authentication endpoint testing completed!');
}

runTests();
