name = "bitebase-backend"
main = "worker.js"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]
account_id = "dc95c232d76cc4df23a5ca452a4046ab"

[env.production]
name = "bitebase-backend-prod"

# Custom domain setup (disabled for now - domain not in current account)
# custom_domains = ["api.bitebase.app"]

[[env.production.d1_databases]]
binding = "DB"
database_name = "bitebase-production-v2"
database_id = "92dda6a5-b416-47a9-a9da-9019f08575be"

# Environment variables (configure these in Cloudflare dashboard)
[env.production.vars]
NODE_ENV = "production"
CORS_ORIGIN = "https://beta.bitebase.app"
FRONTEND_URL = "https://beta.bitebase.app"
API_ROUTE_PREFIX = "/api/v1"
DEBUG = "false"
ENABLE_CROSS_REGION_INFERENCE = "true"
ENABLE_APPLICATION_INFERENCE_PROFILES = "true"
BEDROCK_API_BASE_URL = "https://bedrock-proxy.bitebase.app/api/v1"
BEDROCK_CHAT_MODEL = "anthropic.claude-3-sonnet-********-v1:0"
BEDROCK_REASONING_MODEL = "anthropic.claude-3-7-sonnet-********-v1:0"
BEDROCK_FAST_MODEL = "anthropic.claude-3-haiku-********-v1:0"
BEDROCK_EMBEDDING_MODEL = "cohere.embed-multilingual-v3"
AWS_REGION = "us-east-1"

# Secrets (configure these via wrangler secret put)
# wrangler secret put DATABASE_URL
# wrangler secret put POSTGRES_URL
# wrangler secret put REDIS_URL
# wrangler secret put MAPBOX_API_KEY
# wrangler secret put FOURSQUARE_API_KEY
# wrangler secret put BEDROCK_API_KEY
# wrangler secret put JWT_SECRET
# wrangler secret put AWS_ACCESS_KEY_ID
# wrangler secret put AWS_SECRET_ACCESS_KEY

[build]
command = "npm run build"